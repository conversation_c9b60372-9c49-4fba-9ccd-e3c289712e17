# High-Performance Centralized Logging System

A high-performance, global logging system for the BladeRF video processing project that manages log levels across all components with minimal overhead.

## Overview

This logging system provides centralized control over logging verbosity for different components of the BladeRF video processing pipeline. Each component can have its own independent log level setting, allowing fine-grained control over debugging output. The system is optimized for maximum performance by eliminating thread safety overhead.

## Features

- **High-Performance**: No mutex overhead - optimized for speed over thread safety
- **Component-specific**: Different log levels for different system components
- **Convenient Macros**: Easy-to-use logging macros that check levels before evaluation
- **Global control**: Ability to set all loggers to the same level at once
- **Node.js integration**: Exposed to JavaScript through the Node.js addon
- **Zero dependencies**: Uses only standard C++ library components

## ⚠️ Thread Safety Notice

**This logging system is NOT thread-safe by design for maximum performance.** The application must ensure thread-safe usage at a higher level if multiple threads will be modifying log levels simultaneously. Reading log levels and using the logging macros is safe from multiple threads as long as no thread is modifying the log levels.

## Logger Types

The system supports the following logger categories:

| Logger Type | ID | Description |
|-------------|----|-----------| 
| `GENERAL` | 0 | General system logging |
| `BLADERF_STREAM` | 1 | BladeRF hardware streaming |
| `VIDEO_PROCESSOR` | 2 | Video processing pipeline |
| `SIGNAL_PROCESSING` | 3 | Signal processing algorithms |
| `STREAM_PIPELINE` | 4 | Stream pipeline components |
| `CHUNK_PROCESSOR` | 5 | Chunk processing operations |
| `WAV_STREAM` | 6 | WAV file streaming |
| `NODE_ADDON` | 7 | Node.js addon interface |

## Log Levels

Three log levels are supported:

| Level | Value | Description |
|-------|-------|-------------|
| `NONE` | 0 | No logging output |
| `WARNING` | 1 | Only warnings and errors |
| `VERBOSE` | 2 | Detailed logging information |

## C++ API

### Include the Header

```cpp
#include "logging/logging.h"
```

### High-Performance Logging Macros (Recommended)

The most efficient way to use the logging system is through the provided macros:

```cpp
// Basic logging macros - check level before evaluation
LOG_WARNING(VIDEO_PROCESSOR, "Processing error: " << errorCode);
LOG_VERBOSE(BLADERF_STREAM, "Received " << sampleCount << " samples");
LOG_NONE(GENERAL, "Critical system error: " << message);

// Conditional logging - for performance-critical code
LOG_WARNING_IF(SIGNAL_PROCESSING, errorOccurred, "Signal processing failed");
LOG_VERBOSE_IF(CHUNK_PROCESSOR, debugMode, "Chunk " << chunkId << " processed");

// Manual level checking for complex logic
if (IS_VERBOSE_ENABLED(VIDEO_PROCESSOR)) {
    // Expensive computation only if verbose logging is enabled
    std::string detailedInfo = generateDetailedReport();
    std::cout << "[VIDEO_PROCESSOR][VERBOSE] " << detailedInfo << std::endl;
}
```

### Basic API Functions

```cpp
// Set log level for a specific component
Logging::setLogLevel(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::VERBOSE);

// Get current log level
Logging::LogLevel level = Logging::getLogLevel(Logging::LoggerType::VIDEO_PROCESSOR);

// Check if a log level is enabled
if (Logging::isLogLevelEnabled(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::WARNING)) {
    // Log warning message
}

// Set all loggers to the same level
Logging::setGlobalLogLevel(Logging::LogLevel::WARNING);

// Reset all loggers to default (WARNING)
Logging::resetLogLevels();
```

### Performance Considerations

- **Use macros**: The logging macros are optimized to check log levels before evaluating expressions
- **No thread safety overhead**: Direct memory access for maximum speed
- **Minimal function call overhead**: Inline functions where possible
- **Conditional evaluation**: Expensive logging expressions are only evaluated when needed

## Node.js API

The logging system is exposed to JavaScript through the `setLogLevel` function:

```javascript
const addon = require('./build/Debug/bladerf_addon.node');

// Set GENERAL logger to VERBOSE level
addon.setLogLevel(0, 2); // Returns true on success

// Set BLADERF_STREAM logger to NONE level  
addon.setLogLevel(1, 0); // Returns true on success

// Error handling
try {
    addon.setLogLevel(999, 1); // Invalid logger type
} catch (error) {
    console.error('Error:', error.message);
}
```

### JavaScript Constants

For better readability, you can define constants:

```javascript
const LoggerType = {
    GENERAL: 0,
    BLADERF_STREAM: 1,
    VIDEO_PROCESSOR: 2,
    SIGNAL_PROCESSING: 3,
    STREAM_PIPELINE: 4,
    CHUNK_PROCESSOR: 5,
    WAV_STREAM: 6,
    NODE_ADDON: 7
};

const LogLevel = {
    NONE: 0,
    WARNING: 1,
    VERBOSE: 2
};

// Usage
addon.setLogLevel(LoggerType.VIDEO_PROCESSOR, LogLevel.VERBOSE);
```

## Available Logging Macros

### Basic Logging Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `LOG_NONE(logger_type, message)` | Log at NONE level | `LOG_NONE(GENERAL, "Critical error")` |
| `LOG_WARNING(logger_type, message)` | Log at WARNING level | `LOG_WARNING(BLADERF_STREAM, "Device timeout")` |
| `LOG_VERBOSE(logger_type, message)` | Log at VERBOSE level | `LOG_VERBOSE(VIDEO_PROCESSOR, "Frame processed")` |

### Conditional Logging Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `LOG_WARNING_IF(logger_type, condition, message)` | Log warning if condition is true | `LOG_WARNING_IF(GENERAL, error, "Error occurred")` |
| `LOG_VERBOSE_IF(logger_type, condition, message)` | Log verbose if condition is true | `LOG_VERBOSE_IF(SIGNAL_PROCESSING, debug, "Debug info")` |

### Level Check Macros

| Macro | Description | Example |
|-------|-------------|---------|
| `IS_LOG_ENABLED(logger_type, log_level)` | Check if specific level is enabled | `IS_LOG_ENABLED(GENERAL, WARNING)` |
| `IS_WARNING_ENABLED(logger_type)` | Check if WARNING level is enabled | `IS_WARNING_ENABLED(VIDEO_PROCESSOR)` |
| `IS_VERBOSE_ENABLED(logger_type)` | Check if VERBOSE level is enabled | `IS_VERBOSE_ENABLED(CHUNK_PROCESSOR)` |

### Macro Usage Examples

```cpp
// Simple logging with stream operators
LOG_WARNING(BLADERF_STREAM, "Failed to read " << sampleCount << " samples");

// Conditional logging to avoid expensive operations
LOG_VERBOSE_IF(VIDEO_PROCESSOR, frameCount % 100 == 0,
               "Processed " << frameCount << " frames");

// Manual control for complex scenarios
if (IS_VERBOSE_ENABLED(SIGNAL_PROCESSING)) {
    auto stats = calculateExpensiveStatistics();
    LOG_VERBOSE(SIGNAL_PROCESSING, "Statistics: " << stats.toString());
}
```

## Implementation Details

### File Structure

- `logging.h` - Header file with declarations and inline functions
- `logging.cpp` - Implementation file with function definitions
- `.gitignore` - Ignore build artifacts

### Memory Layout

- Global array of log levels (one per logger type)
- Direct memory access with no synchronization overhead
- Static initialization ensures proper startup

### Performance Optimizations

- **Zero mutex overhead**: Direct memory access for maximum speed
- **Inline functions**: `getLogLevel()` and `isLogLevelEnabled()` optimized for frequent calls
- **Macro-based logging**: Level checks before expensive expression evaluation
- **No dynamic allocation**: All memory is statically allocated

## Build Integration

The logging system is automatically built with both CMake and node-gyp:

### CMakeLists.txt
```cmake
# Already included in source list
src/logging/logging.cpp
```

### binding.gyp
```json
{
  "sources": [
    "src/logging/logging.cpp"
  ]
}
```

## Default Behavior

- All loggers start with `WARNING` level by default
- Static initialization occurs automatically
- No configuration required for basic usage
- High-performance operation out of the box

## Error Handling

The Node.js interface provides comprehensive error checking:

- Parameter count validation
- Parameter type validation  
- Range checking for logger types and log levels
- Descriptive error messages for debugging
