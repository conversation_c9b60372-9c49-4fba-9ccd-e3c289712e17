# Centralized Logging System

A thread-safe, global logging system for the BladeRF video processing project that manages log levels across all components.

## Overview

This logging system provides centralized control over logging verbosity for different components of the BladeRF video processing pipeline. Each component can have its own independent log level setting, allowing fine-grained control over debugging output.

## Features

- **Thread-safe**: All operations are protected by mutexes for safe multi-threaded access
- **Component-specific**: Different log levels for different system components
- **Global control**: Ability to set all loggers to the same level at once
- **Node.js integration**: Exposed to JavaScript through the Node.js addon
- **Zero dependencies**: Uses only standard C++ library components

## Logger Types

The system supports the following logger categories:

| Logger Type | ID | Description |
|-------------|----|-----------| 
| `GENERAL` | 0 | General system logging |
| `BLADERF_STREAM` | 1 | BladeRF hardware streaming |
| `VIDEO_PROCESSOR` | 2 | Video processing pipeline |
| `SIGNAL_PROCESSING` | 3 | Signal processing algorithms |
| `STREAM_PIPELINE` | 4 | Stream pipeline components |
| `CHUNK_PROCESSOR` | 5 | Chunk processing operations |
| `WAV_STREAM` | 6 | WAV file streaming |
| `NODE_ADDON` | 7 | Node.js addon interface |

## Log Levels

Three log levels are supported:

| Level | Value | Description |
|-------|-------|-------------|
| `NONE` | 0 | No logging output |
| `WARNING` | 1 | Only warnings and errors |
| `VERBOSE` | 2 | Detailed logging information |

## C++ API

### Include the Header

```cpp
#include "logging/logging.h"
```

### Basic Usage

```cpp
// Set log level for a specific component
Logging::setLogLevel(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::VERBOSE);

// Get current log level
Logging::LogLevel level = Logging::getLogLevel(Logging::LoggerType::VIDEO_PROCESSOR);

// Check if a log level is enabled
if (Logging::isLogLevelEnabled(Logging::LoggerType::VIDEO_PROCESSOR, Logging::LogLevel::WARNING)) {
    // Log warning message
}

// Set all loggers to the same level
Logging::setGlobalLogLevel(Logging::LogLevel::WARNING);

// Reset all loggers to default (WARNING)
Logging::resetLogLevels();
```

### Thread Safety

All functions are thread-safe and can be called from multiple threads simultaneously:

```cpp
// Safe to call from multiple threads
std::thread t1([]() {
    Logging::setLogLevel(Logging::LoggerType::GENERAL, Logging::LogLevel::VERBOSE);
});

std::thread t2([]() {
    Logging::LogLevel level = Logging::getLogLevel(Logging::LoggerType::GENERAL);
});
```

## Node.js API

The logging system is exposed to JavaScript through the `setLogLevel` function:

```javascript
const addon = require('./build/Debug/bladerf_addon.node');

// Set GENERAL logger to VERBOSE level
addon.setLogLevel(0, 2); // Returns true on success

// Set BLADERF_STREAM logger to NONE level  
addon.setLogLevel(1, 0); // Returns true on success

// Error handling
try {
    addon.setLogLevel(999, 1); // Invalid logger type
} catch (error) {
    console.error('Error:', error.message);
}
```

### JavaScript Constants

For better readability, you can define constants:

```javascript
const LoggerType = {
    GENERAL: 0,
    BLADERF_STREAM: 1,
    VIDEO_PROCESSOR: 2,
    SIGNAL_PROCESSING: 3,
    STREAM_PIPELINE: 4,
    CHUNK_PROCESSOR: 5,
    WAV_STREAM: 6,
    NODE_ADDON: 7
};

const LogLevel = {
    NONE: 0,
    WARNING: 1,
    VERBOSE: 2
};

// Usage
addon.setLogLevel(LoggerType.VIDEO_PROCESSOR, LogLevel.VERBOSE);
```

## Implementation Details

### File Structure

- `logging.h` - Header file with declarations and inline functions
- `logging.cpp` - Implementation file with function definitions
- `.gitignore` - Ignore build artifacts

### Memory Layout

- Global array of log levels (one per logger type)
- Single mutex protecting all access
- Static initialization ensures thread-safe startup

### Performance Considerations

- `getLogLevel()` and `isLogLevelEnabled()` are optimized for frequent calls
- Mutex contention is minimized through efficient locking
- No dynamic memory allocation

## Build Integration

The logging system is automatically built with both CMake and node-gyp:

### CMakeLists.txt
```cmake
# Already included in source list
src/logging/logging.cpp
```

### binding.gyp
```json
{
  "sources": [
    "src/logging/logging.cpp"
  ]
}
```

## Default Behavior

- All loggers start with `WARNING` level by default
- Thread-safe initialization occurs automatically
- No configuration required for basic usage

## Error Handling

The Node.js interface provides comprehensive error checking:

- Parameter count validation
- Parameter type validation  
- Range checking for logger types and log levels
- Descriptive error messages for debugging
