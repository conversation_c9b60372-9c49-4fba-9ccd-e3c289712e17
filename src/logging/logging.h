#pragma once

#include <cstdint>
#include <mutex>

namespace Logging {

/**
 * LogLevel - Standardized logging levels for the BladeRF video processing system
 * 
 * Represents different verbosity levels for logging output across all components.
 */
enum class LogLevel: int8_t {
  NONE = 0,      // No logging output
  WARNING = 1,   // Only warnings and errors
  VERBOSE = 2,   // Detailed logging information
};

/**
 * LoggerType - Identifies different logger categories in the system
 * 
 * Each logger type can have its own independent log level setting.
 */
enum class LoggerType: int8_t {
  GENERAL = 0,           // General system logging
  BLADERF_STREAM = 1,    // BladeRF hardware streaming
  VIDEO_PROCESSOR = 2,   // Video processing pipeline
  SIGNAL_PROCESSING = 3, // Signal processing algorithms
  STREAM_PIPELINE = 4,   // Stream pipeline components
  CHUNK_PROCESSOR = 5,   // Chunk processing operations
  WAV_STREAM = 6,        // WAV file streaming
  NODE_ADDON = 7,        // Node.js addon interface
};

/**
 * Global logging configuration functions
 * 
 * These functions provide thread-safe access to global log level settings
 * for different logger categories throughout the system.
 */

/**
 * Set the log level for a specific logger type
 * @param loggerType The logger category to configure
 * @param level The log level to set for this logger
 */
void setLogLevel(LoggerType loggerType, LogLevel level);

/**
 * Get the current log level for a specific logger type
 * @param loggerType The logger category to query
 * @return The current log level for the specified logger
 */
LogLevel getLogLevel(LoggerType loggerType);

/**
 * Check if a specific log level is enabled for a logger type
 * @param loggerType The logger category to check
 * @param level The log level to test
 * @return true if the specified level is enabled for this logger
 */
bool isLogLevelEnabled(LoggerType loggerType, LogLevel level);

/**
 * Set log levels for all loggers to the same value
 * @param level The log level to apply to all loggers
 */
void setGlobalLogLevel(LogLevel level);

/**
 * Reset all log levels to their default values (WARNING)
 */
void resetLogLevels();

/**
 * Get the total number of logger types available
 * @return The count of available logger types
 */
constexpr int getLoggerTypeCount() {
    return static_cast<int>(LoggerType::NODE_ADDON) + 1;
}

} // namespace Logging
