#pragma once

#include <cstdint>
#include <iostream>
#include <array>

namespace Logging {

/**
 * LogLevel - Standardized logging levels for the BladeRF video processing system
 * 
 * Represents different verbosity levels for logging output across all components.
 */
enum class LogLevel: int8_t {
  NONE = 0,      // No logging output
  WARNING = 1,   // Only warnings and errors
  VERBOSE = 2,   // Detailed logging information
};

/**
 * LoggerType - Identifies different logger categories in the system
 * 
 * Each logger type can have its own independent log level setting.
 */
enum class LoggerType: int8_t {
    GENERAL = 0,           // General system logging
    NODE_ADDON = 7,        // Node.js addon interface
    VIDEO_PROCESSOR_WRAPPER = 8,        // Video synchronization logic

    BLADERF_STREAM = 1,    // BladeRF hardware streaming
    VIDEO_PROCESSOR = 2,   // Video processing pipeline
    SIGNAL_PROCESSING = 3, // Signal processing algorithms
    STREAM_PIPELINE = 4,   // Stream pipeline components
    CHUNK_PROCESSOR = 5,   // Chunk processing operations
    WAV_STREAM = 6,        // WAV file streaming

    THE_LAST_ITEM = 20, // Sentinel value for array sizing
};

/**
 * Get the total number of logger types available
 * @return The count of available logger types
 */
constexpr int getLoggerTypeCount() {
    return static_cast<int>(LoggerType::NODE_ADDON) + 1;
}

// External declaration of the global log levels array (defined in logging.cpp)
extern std::array<LogLevel, getLoggerTypeCount()> g_logLevels;

/**
 * Global logging configuration functions
 *
 * These functions provide high-performance access to global log level settings
 * for different logger categories throughout the system.
 *
 * NOTE: These functions are NOT thread-safe for performance reasons.
 * The application must ensure thread-safe usage at a higher level.
 */

/**
 * Set the log level for a specific logger type
 * @param loggerType The logger category to configure
 * @param level The log level to set for this logger
 */
inline void setLogLevel(LoggerType loggerType, LogLevel level) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        g_logLevels[index] = level;
    }
}

/**
 * Get the current log level for a specific logger type
 * @param loggerType The logger category to query
 * @return The current log level for the specified logger
 */
inline LogLevel getLogLevel(LoggerType loggerType) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        return g_logLevels[index];
    }
    return LogLevel::WARNING; // Default fallback
}

/**
 * Check if a specific log level is enabled for a logger type
 * @param loggerType The logger category to check
 * @param level The log level to test
 * @return true if the specified level is enabled for this logger
 */
inline bool isLogLevelEnabled(LoggerType loggerType, LogLevel level) {
    const LogLevel currentLevel = getLogLevel(loggerType);
    return static_cast<int8_t>(level) <= static_cast<int8_t>(currentLevel);
}

/**
 * Set log levels for all loggers to the same value
 * @param level The log level to apply to all loggers
 */
inline void setGlobalLogLevel(LogLevel level) {
    for (auto& logLevel : g_logLevels) {
        logLevel = level;
    }
}

/**
 * Reset all log levels to their default values (WARNING)
 */
inline void resetLogLevels() {
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
}

} // namespace Logging

/**
 * High-Performance Logging Macros
 *
 * These macros provide convenient logging with minimal overhead by checking
 * log levels before evaluating expensive expressions or performing I/O.
 *
 * Usage Examples:
 *   LOG_WARNING(GENERAL, "Error code: " << errorCode);
 *   LOG_VERBOSE(VIDEO_PROCESSOR, "Processing frame " << frameNum);
 *   LOG_NONE(BLADERF_STREAM, "Critical error: " << message);
 */

// Core logging macro that checks level before executing
#define LOG_IF_ENABLED(logger_type, log_level, message) \
    do { \
        if (Logging::isLogLevelEnabled(Logging::LoggerType::logger_type, Logging::LogLevel::log_level)) { \
            std::cout << "[" #logger_type "][" #log_level "] " << message << std::endl; \
        } \
    } while(0)

// Convenience macros for each log level
#define LOG_NONE(logger_type, message) \
    LOG_IF_ENABLED(logger_type, NONE, message)

#define LOG_WARNING(logger_type, message) \
    LOG_IF_ENABLED(logger_type, WARNING, message)

#define LOG_VERBOSE(logger_type, message) \
    LOG_IF_ENABLED(logger_type, VERBOSE, message)

// Conditional logging macros for performance-critical code
#define LOG_WARNING_IF(logger_type, condition, message) \
    do { \
        if ((condition) && Logging::isLogLevelEnabled(Logging::LoggerType::logger_type, Logging::LogLevel::WARNING)) { \
            std::cout << "[" #logger_type "][WARNING] " << message << std::endl; \
        } \
    } while(0)

#define LOG_VERBOSE_IF(logger_type, condition, message) \
    do { \
        if ((condition) && Logging::isLogLevelEnabled(Logging::LoggerType::logger_type, Logging::LogLevel::VERBOSE)) { \
            std::cout << "[" #logger_type "][VERBOSE] " << message << std::endl; \
        } \
    } while(0)

// Fast check macros for manual control
#define IS_LOG_ENABLED(logger_type, log_level) \
    Logging::isLogLevelEnabled(Logging::LoggerType::logger_type, Logging::LogLevel::log_level)

#define IS_WARNING_ENABLED(logger_type) \
    IS_LOG_ENABLED(logger_type, WARNING)

#define IS_VERBOSE_ENABLED(logger_type) \
    IS_LOG_ENABLED(logger_type, VERBOSE)
