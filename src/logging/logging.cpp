#include "logging.h"
#include <array>

namespace Logging {

// Global storage for log levels - one for each logger type
// Protected by mutex for thread safety
static std::array<LogLevel, getLoggerTypeCount()> g_logLevels;
static std::mutex g_logLevelsMutex;

// Initialize all log levels to WARNING by default
static bool g_initialized = []() {
    std::lock_guard<std::mutex> lock(g_logLevelsMutex);
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
    return true;
}();

void setLogLevel(LoggerType loggerType, LogLevel level) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        std::lock_guard<std::mutex> lock(g_logLevelsMutex);
        g_logLevels[index] = level;
    }
}

LogLevel getLogLevel(LoggerType loggerType) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        std::lock_guard<std::mutex> lock(g_logLevelsMutex);
        return g_logLevels[index];
    }
    return LogLevel::WARNING; // Default fallback
}

bool isLogLevelEnabled(LoggerType loggerType, LogLevel level) {
    const LogLevel currentLevel = getLogLevel(loggerType);
    return static_cast<int8_t>(level) <= static_cast<int8_t>(currentLevel);
}

void setGlobalLogLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(g_logLevelsMutex);
    for (auto& logLevel : g_logLevels) {
        logLevel = level;
    }
}

void resetLogLevels() {
    std::lock_guard<std::mutex> lock(g_logLevelsMutex);
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
}

} // namespace Logging
