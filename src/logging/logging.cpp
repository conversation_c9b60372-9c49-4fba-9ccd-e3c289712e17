#include "logging.h"
#include <array>

namespace Logging {

// Global storage for log levels - one for each logger type
// NOTE: NOT thread-safe for maximum performance - application must ensure thread safety
static std::array<LogLevel, getLoggerTypeCount()> g_logLevels;

// Initialize all log levels to WARNING by default
static bool g_initialized = []() {
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
    return true;
}();

void setLogLevel(LoggerType loggerType, LogLevel level) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        g_logLevels[index] = level;
    }
}

LogLevel getLogLevel(LoggerType loggerType) {
    const int index = static_cast<int>(loggerType);
    if (index >= 0 && index < getLoggerTypeCount()) {
        return g_logLevels[index];
    }
    return LogLevel::WARNING; // Default fallback
}

bool isLogLevelEnabled(LoggerType loggerType, LogLevel level) {
    const LogLevel currentLevel = getLogLevel(loggerType);
    return static_cast<int8_t>(level) <= static_cast<int8_t>(currentLevel);
}

void setGlobalLogLevel(LogLevel level) {
    for (auto& logLevel : g_logLevels) {
        logLevel = level;
    }
}

void resetLogLevels() {
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
}

} // namespace Logging
