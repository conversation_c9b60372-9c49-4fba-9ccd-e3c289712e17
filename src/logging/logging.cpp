#include "logging.h"
#include <array>

namespace Logging {

// Global storage for log levels - one for each logger type
// NOTE: NOT thread-safe for maximum performance - application must ensure thread safety
// This is the only definition of the global array to avoid multiple definition errors
std::array<LogLevel, getLoggerTypeCount()> g_logLevels;

// Initialize all log levels to WARNING by default
static bool g_initialized = []() {
    for (auto& level : g_logLevels) {
        level = LogLevel::WARNING;
    }
    return true;
}();

} // namespace Logging
