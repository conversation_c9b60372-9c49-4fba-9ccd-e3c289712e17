#include <node.h>
#include <iostream>
#include "video_processor_wrapper.h"
#include "wav-stream/wav_stream.h"
#include "logging/logging.h"

namespace VideoDecodingAddon {

/**
 * NODE SPECIFIC IMPLEMENTATION AND METHODS
 */
v8::Persistent<v8::Function> VideoProcessorWrapper::constructor;

void VideoProcessorWrapper::Node_Init(const v8::Local<v8::Object> exports) {
  v8::Isolate* isolate = exports->GetIsolate();

  // Prepare constructor template
  const v8::Local<v8::FunctionTemplate> tpl = v8::FunctionTemplate::New(isolate, Node_New);
  tpl->SetClassName(v8::String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked());
  tpl->InstanceTemplate()->SetInternalFieldCount(1);

  // Prototype methods
  NODE_SET_PROTOTYPE_METHOD(tpl, "stop", Stop_Prototype);

  constructor.Reset(isolate, tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked());
  exports->Set(
    isolate->GetCurrentContext(),
    v8::String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked(),
    tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked()
  ).Check();

  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, "Video processor class initialized in Node.js");
}

void VideoProcessorWrapper::Node_New(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Isolate* isolate = args.GetIsolate();

  if (args.IsConstructCall()) {
    // Invoked as constructor: `new VideoProcessor(...)`
    auto* obj = new VideoProcessorWrapper();
    obj->Wrap(args.This());
    args.GetReturnValue().Set(args.This());
  } else {
    // Invoked as plain function `VideoProcessor(...)`, turn into construct call.
    constexpr int argc = 0;
    v8::Local<v8::Value> argv[1] = {};
    const v8::Local<v8::Function> cons = v8::Local<v8::Function>::New(isolate, constructor);
    const v8::Local<v8::Context> context = isolate->GetCurrentContext();
    const v8::Local<v8::Object> result = cons->NewInstance(context, argc, argv).ToLocalChecked();
    args.GetReturnValue().Set(result);
  }
}

void VideoProcessorWrapper::Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Isolate* isolate = args.GetIsolate();
  LOG_VERBOSE(VIDEO_PROCESSOR_WRAPPER, ".stop() method called");


  auto* videoProcessor = Unwrap<VideoProcessorWrapper>(args.Holder());
  videoProcessor->stop();
  args.GetReturnValue().Set(v8::Boolean::New(isolate, true));
}

/**
 * INSTANCE METHODS
 */
VideoProcessorWrapper::~VideoProcessorWrapper() {
  stop();
}

bool VideoProcessorWrapper::start(const v8::Local<v8::Object> configObject) {
  if (!initializeNodeCallbacks(configObject)) {
    return false;
  }

  auto iqStream = createIQStream(configObject);
  if (!iqStream) {
    std::cerr << "Failed to create IQ stream from configuration" << std::endl;
    return false;
  }

  // Create VideoProcessor instance
  videoProcessor_ = std::make_unique<IQVideoProcessor::VideoProcessor>(std::move(iqStream), [this]() {
    // Called on producer thread: enqueue work for Node thread
    if (!nodeRunner_ || !videoProcessor_) return;
    // Check if there are frames to emit, and schedule it in the Node thread
    if (videoProcessor_->hasNextFrame()) {
      nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) {
        this->emitFramesToNode();
      });
    }
    // TODO: Implement other checks
  });

  if (!videoProcessor_->start()) {
    std::cerr << "Failed to start video processor." << std::endl;
    videoProcessor_.reset();
    // TODO: Implement error posting
    return false;
  }

  // Notify "ready" on Node thread
  if (nodeRunner_) {
    nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) {
      processEventCallbacks();
    });
  }

  return true;
}

void VideoProcessorWrapper::stop() {
  stop(0); // Default stop reason
}

void VideoProcessorWrapper::stop(int reason) {
  stopReason_ = reason;

  if (videoProcessor_) {
    videoProcessor_->stop();
    videoProcessor_.reset();
  }

  // stop() is typically called on the Node thread; call directly
  processStopCallbacks();

  if (nodeRunner_) {
    nodeRunner_->disable();
    nodeRunner_.reset();
  }

  onEventCallback_.Reset();
  onStopCallback_.Reset();
  onErrorCallback_.Reset();
  onFrameCallback_.Reset();
}



void VideoProcessorWrapper::emitFramesToNode() const {
  // We are in the main Node.js thread here, safe to call V8 APIs
  if (!videoProcessor_ || onFrameCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onFrameCallback_);

  while (videoProcessor_->hasNextFrame()) {
    const auto& frame = videoProcessor_->getNextFrame(); // Consume the frame from the queue
    // Creating JavaScript object for the frame, packing the image into the ArrayBuffer
    const v8::Local<v8::Object> frameObj = v8::Object::New(isolate);
    const v8::Local<v8::ArrayBuffer> buffer = v8::ArrayBuffer::New(isolate, frame.dataSize);
    std::memcpy(buffer->GetBackingStore()->Data(), frame.data.data(), frame.dataSize);
    const v8::Local<v8::Uint8Array> imageBuffer = v8::Uint8Array::New(buffer, 0, frame.dataSize);
    // Setting properties
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "image").ToLocalChecked(), imageBuffer).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "frameNumber").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.frameNumber))).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "width").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.width))).Check();
    frameObj->Set(context, v8::String::NewFromUtf8(isolate, "height").ToLocalChecked(), v8::Number::New(isolate, static_cast<double>(frame.height))).Check();
    // Call the JavaScript callback function
    v8::Local<v8::Value> argv[1] = { frameObj };
    if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
      std::cerr << "Error calling JavaScript frame callback" << std::endl;
    }
  }
}

void VideoProcessorWrapper::processEventCallbacks() const {
  if (onEventCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onEventCallback_);

  v8::Local<v8::Value> argv[1] = { v8::String::NewFromUtf8(isolate, "ready").ToLocalChecked() };
  if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
    std::cerr << "Error calling JavaScript onEvent callback" << std::endl;
  }
}

void VideoProcessorWrapper::processStopCallbacks() const {
  if (onStopCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onStopCallback_);

  v8::Local<v8::Value> argv[1] = { v8::Number::New(isolate, static_cast<double>(stopReason_)) };
  if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
    std::cerr << "Error calling JavaScript onStop callback" << std::endl;
  }
}

void VideoProcessorWrapper::processErrorCallbacks() const {
  if (onErrorCallback_.IsEmpty()) return;

  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();
  const v8::Local<v8::Function> callbackFunction = v8::Local<v8::Function>::New(isolate, onErrorCallback_);

  v8::Local<v8::Value> argv[1] = { v8::String::NewFromUtf8(isolate, "VideoProcessor error occurred").ToLocalChecked() };
  if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
    std::cerr << "Error calling JavaScript onError callback" << std::endl;
  }
}





/**
 * Initialize Node.js callbacks from the provided configuration object
 * Expects the configObject to have the following properties:
 * - onEvent: function to call on events (e.g., "ready")
 * - onStop: function to call when processing stops
 * - onError: function to call on errors
 * - onFrame: function to call when a new frame is available
 * Returns true if all callbacks are successfully initialized, false otherwise
 */

bool VideoProcessorWrapper::initializeNodeCallbacks(const v8::Local<v8::Object>& configObject) {
  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  const v8::Local<v8::Context> context = isolate->GetCurrentContext();

  // Extract callbacks from configuration object
  const v8::Local<v8::String> onEventKey = v8::String::NewFromUtf8(isolate, "onEvent").ToLocalChecked();
  const v8::Local<v8::String> onStopKey = v8::String::NewFromUtf8(isolate, "onStop").ToLocalChecked();
  const v8::Local<v8::String> onErrorKey = v8::String::NewFromUtf8(isolate, "onError").ToLocalChecked();
  const v8::Local<v8::String> onFrameKey = v8::String::NewFromUtf8(isolate, "onFrame").ToLocalChecked();

  v8::Local<v8::Value> onEventVal, onStopVal, onErrorVal, onFrameVal;
  if (!configObject->Get(context, onEventKey).ToLocal(&onEventVal) ||
      !configObject->Get(context, onStopKey).ToLocal(&onStopVal) ||
      !configObject->Get(context, onErrorKey).ToLocal(&onErrorVal) ||
      !configObject->Get(context, onFrameKey).ToLocal(&onFrameVal)
  ) {
    std::cerr << "VideoProcessorWrapper::initializeNodeCallbacks() failed to get one or more callback properties from configObject" << std::endl;
    return false;
  }

  // Validate that all callbacks are functions
  if (!onEventVal->IsFunction() || !onStopVal->IsFunction() || !onErrorVal->IsFunction() || !onFrameVal->IsFunction()) {
    std::cerr << "VideoProcessorWrapper::initializeNodeCallbacks() one or more callback properties is not a function" << std::endl;
    return false;
  }

  // Store the callbacks persistently
  onEventCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onEventVal));
  onStopCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onStopVal));
  onErrorCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onErrorVal));
  onFrameCallback_.Reset(isolate, v8::Local<v8::Function>::Cast(onFrameVal));

  // Initialize the ThreadRunner for this instance
  nodeRunner_ = std::make_unique<NodeHelpers::NodeThreadRunner>(isolate);
  if (!nodeRunner_->enable()) {
    std::cerr << "VideoProcessorWrapper::initializeNodeCallbacks failed to enable NodeThreadRunner" << std::endl;
    return false;
  }
  return true;
}

std::unique_ptr<IIQStream> VideoProcessorWrapper::createIQStream(const v8::Local<v8::Object>& configObject) const {
  v8::Isolate* isolate = v8::Isolate::GetCurrent();
  const v8::Local<v8::Context> context = isolate->GetCurrentContext(); // TODO Use it when accessing properties of configObject

  // // Create WAV stream data source
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/spectrozir_6.5mhz.wav", true, true);
  auto wavStream = std::make_unique<WAVIQStream>("samples/recording.wav", true, true);
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/bladerf_2.wav", true, true);

  if (!wavStream->open()) {
    std::cerr << "Failed to open WAV file: " << wavStream->lastError() << std::endl;
    return nullptr;
  }
  return std::move(wavStream);
}

} // namespace VideoDecodingAddon
