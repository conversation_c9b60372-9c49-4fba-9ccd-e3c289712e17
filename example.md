### Short usage example and description

Goal: Post work from any C++ thread to run on Node’s main thread with a proper V8 scope, using NodeThreadRunner. Use it to replace per-callback uv_async_t boilerplate and call your existing JS-facing methods safely.

Setup:
- Include and hold a NodeThreadRunner in your wrapper class.
- Enable it when you start; disable it when you stop or destroy.
- Post lambdas capturing “this” from any thread; they will execute on the Node thread with v8::HandleScope and v8::Context::Scope already entered.

Example integration with your VideoProcessorWrapper:

Header:
```cpp
// video_processor_wrapper.h
#include "node_thread_runner.h"

class VideoProcessorWrapper : public node::ObjectWrap {
  // ...
private:
  std::unique_ptr<NodeThreadRunner> nodeRunner_;

  void processFramesCallbacks() const;
  void processEventCallbacks() const;
  void processStopCallbacks() const;
  void processErrorCallbacks() const;
  // ...
};
```

Start:
```cpp
bool VideoProcessorWrapper::start(v8::Local<v8::Object> configObject) {
  v8::Isolate* isolate = v8::Isolate::GetCurrent();

  // ... extract and store v8::Persistent callbacks ...

  // Initialize the runner for this instance
  nodeRunner_ = std::make_unique<NodeThreadRunner>(isolate /*, node::GetCurrentEventLoop(isolate)*/);
  if (!nodeRunner_->enable()) return false;

  // Set up your producer to schedule frame processing onto Node’s thread
  videoProcessor_ = std::make_unique<IQVideoProcessor::VideoProcessor>(
    std::move(wavStream),
    [this]() {
      // Called on producer thread: enqueue work for Node thread
      if (nodeRunner_) {
        nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) {
          processFramesCallbacks();
        });
      }
    }
  );

  if (!videoProcessor_->start()) {
    // Post error to Node thread
    if (nodeRunner_) {
      nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) { processErrorCallbacks(); });
    }
    return false;
  }

  // Notify “ready” on Node thread
  nodeRunner_->run([this](v8::Isolate*, v8::Local<v8::Context>) { processEventCallbacks(); });
  return true;
}
```

Stop/teardown:
```cpp
void VideoProcessorWrapper::stop(int reason) {
  stopReason_ = reason;

  if (videoProcessor_) {
    videoProcessor_->stop();
    videoProcessor_.reset();
  }

  // stop() is typically called on the Node thread; call directly or post then disable:
  processStopCallbacks();

  if (nodeRunner_) {
    nodeRunner_->disable();
    nodeRunner_.reset();
  }

  onEventCallback_.Reset();
  onStopCallback_.Reset();
  onErrorCallback_.Reset();
  onFrameCallback_.Reset();
}
```

Posting any task from arbitrary threads:
```cpp
// From any background thread:
nodeRunner_->run([this](v8::Isolate* isolate, v8::Local<v8::Context> ctx) {
  // Safe V8 work here; HandleScope and Context::Scope are already set.
  processErrorCallbacks();
});
```

What the Agent should remember:
- NodeThreadRunner ensures thread-safe enqueue and execution on the Node main thread.
- It owns one uv_async_t and a lock-protected queue; run() is O(1) with minimal lock time.
- All posted lambdas run under a V8 HandleScope and the current Context on the Node thread.
- Call enable() before posting; disable() to stop accepting, clear pending tasks, and close the handle.
- This replaces multiple uv_async_t handles and static callbacks with a single, reusable runner.